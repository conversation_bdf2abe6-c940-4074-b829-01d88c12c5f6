/// <reference types="node" />
import { encodePacket } from "./encodePacket.js";
import { decodePacket } from "./decodePacket.js";
import { Packet, PacketType, RawData, BinaryType } from "./commons.js";
import type { TransformStream } from "node:stream/web";
declare const encodePayload: (packets: Packet[], callback: (encodedPayload: string) => void) => void;
declare const decodePayload: (encodedPayload: string, binaryType?: BinaryType) => Packet[];
export declare function createPacketEncoderStream(): TransformStream<Packet, any>;
export declare function createPacketDecoderStream(maxPayload: number, binaryType: BinaryType): TransformStream<Uint8Array, any>;
export declare const protocol = 4;
export { encodePacket, encodePayload, decodePacket, decodePayload, Packet, PacketType, RawData, BinaryType, };
